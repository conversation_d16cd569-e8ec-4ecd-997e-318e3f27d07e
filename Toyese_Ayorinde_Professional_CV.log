This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  4 SEP 2025 15:26
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./<PERSON><PERSON>_Ayorinde_Professional_CV.tex
(Toy<PERSON>_Ayorinde_Professional_CV.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncv.c
ls
Document Class: moderncv 2024-07-18 v2.4.1 modern curriculum vitae and letter d
ocument class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count194
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/colortbl\colortbl.s
ty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen141
\ar@mcellbox=\box52
\extrarowheight=\dimen142
\NC@list=\toks17
\extratabsurround=\skip49
\backup@length=\skip50
\ar@cellbox=\box53
)
\everycr=\toks18
\minrowclearance=\skip51
\rownum=\count195
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen143
\Gin@req@width=\dimen144
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.s
ty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip52
\f@nch@offset@elh=\skip53
\f@nch@offset@erh=\skip54
\f@nch@offset@olh=\skip55
\f@nch@offset@orh=\skip56
\f@nch@offset@elf=\skip57
\f@nch@offset@erf=\skip58
\f@nch@offset@olf=\skip59
\f@nch@offset@orf=\skip60
\f@nch@height=\skip61
\f@nch@footalignment=\skip62
\f@nch@widthL=\skip63
\f@nch@widthC=\skip64
\f@nch@widthR=\skip65
\@temptokenb=\toks20
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\tweaklist.
sty) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count196
\calc@Bcount=\count197
\calc@Adimen=\dimen145
\calc@Bdimen=\dimen146
\calc@Askip=\skip66
\calc@Bskip=\skip67
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count198
\calc@Cskip=\skip68
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\x
parse.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-05-27 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count199
\l__pdf_internal_box=\box54
))
Package: xparse 2024-05-08 L3 Experimental document command parser
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
.sty
Package: microtype 2024/12/12 v3.2 Micro-typographical refinements (RS)
\MT@toks=\toks21
\MT@tempbox=\box55
\MT@count=\count266
LaTeX Info: Redefining \noprotrusionifhmode on input line 1075.
LaTeX Info: Redefining \leftprotrusion on input line 1076.
\MT@prot@toks=\toks22
LaTeX Info: Redefining \rightprotrusion on input line 1095.
LaTeX Info: Redefining \textls on input line 1437.
\MT@outer@kern=\dimen147
LaTeX Info: Redefining \microtypecontext on input line 2041.
LaTeX Info: Redefining \textmicrotypecontext on input line 2058.
\MT@listname@count=\count267

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
-pdftex.def
File: microtype-pdftex.def 2024/12/12 v3.2 Definitions specific to pdftex (RS)
LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip69
)
Package microtype Info: Loading configuration file microtype.cfg.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
.cfg
File: microtype.cfg 2024/12/12 v3.2 microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3053.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvco
llection.sty
Package: moderncvcollection 2024-07-18 v2.4.1 moderncv collections
\c@collection@iterator=\count268
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvco
mpatibility.sty
Package: moderncvcompatibility 2024-07-18 v2.4.1 modern curriculum vitae and le
tter compatibility patches
)
\c@collection@phones@count=\count269
\c@collection@socials@count=\count270
\c@cvcolumnscounter=\count271
\c@cvcolumnsautowidthcounter=\count272
\c@tmpiteratorcounter=\count273
\cvcolumnsdummywidth=\skip70
\cvcolumnswidth=\skip71
\cvcolumnsautowidth=\skip72
\cvcolumnautowidth=\skip73
\bibindent=\skip74
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvst
ylebanking.sty
Package: moderncvstylebanking 2024-07-18 v2.4.1 modern curriculum vitae and let
ter style scheme: banking

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tex-gyre\tgpagella.
sty
Package: tgpagella 2009/09/27 v1.2 TeX Gyre Pagella as default roman family

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvic
onssymbols.sty
Package: moderncviconssymbols 2021-12-12 v2.2.0 modern curriculum vitae icons s
elector


moderncv: academicons requires XeTeX/LuaTeX to work. Using alternatives.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvic
onsawesome.sty
Package: moderncviconsawesome 2024-07-18 v2.4.1 modern curriculum vitae icons: 
awesome

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontaw
esome5.sty
Package: fontawesome5 2022/05/02 v5.15.4 Font Awesome 5

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/l3keys2e
\l3keys2e.sty
Package: l3keys2e 2024-05-08 LaTeX2e option processing using LaTeX3 keys
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontaw
esome5-generic-helper.sty
Package: fontawesome5-generic-helper 2022/05/02 v5.15.4 non-uTeX helper for fon
tawesome5

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontaw
esome5-mapping.def)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvco
lors.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/moderncv\moderncvic
onstikz.sty
Package: moderncviconstikz 2024-07-18 v2.4.1 modern curriculum vitae and letter
 icons: tickz

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\t
ikz.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrc
s.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
util-common.tex
\pgfutil@everybye=\toks23
\pgfutil@tempdima=\dimen148
\pgfutil@tempdimb=\dimen149
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
util-latex.def
\pgfutil@abb=\box56
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
rcs.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.
tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfc
ore.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgf
sys.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\p
gfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
keys.code.tex
\pgfkeys@pathtoks=\toks24
\pgfkeys@temptoks=\toks25

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
keyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks26
))
\pgf@x=\dimen150
\pgf@y=\dimen151
\pgf@xa=\dimen152
\pgf@ya=\dimen153
\pgf@xb=\dimen154
\pgf@yb=\dimen155
\pgf@xc=\dimen156
\pgf@yc=\dimen157
\pgf@xd=\dimen158
\pgf@yd=\dimen159
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count274
\c@pgf@countb=\count275
\c@pgf@countc=\count276
\c@pgf@countd=\count277
\t@pgf@toka=\toks27
\t@pgf@tokb=\toks28
\t@pgf@tokc=\toks29
\pgf@sys@id@count=\count278

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\p
gf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\p
gfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\p
gfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\p
gfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count279
\pgfsyssoftpath@bigbuffer@items=\count280
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\p
gfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.
code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathu
til.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathp
arser.code.tex
\pgfmath@dimen=\dimen160
\pgfmath@count=\count281
\pgfmath@box=\box57
\pgfmath@toks=\toks30
\pgfmath@stack@operand=\toks31
\pgfmath@stack@operation=\toks32
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
unctions.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathc
alc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathf
loat.code.tex
\c@pgfmathroundto@lastzeros=\count282
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.c
ode.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen161
\pgf@picmaxx=\dimen162
\pgf@picminy=\dimen163
\pgf@picmaxy=\dimen164
\pgf@pathminx=\dimen165
\pgf@pathmaxx=\dimen166
\pgf@pathminy=\dimen167
\pgf@pathmaxy=\dimen168
\pgf@xx=\dimen169
\pgf@xy=\dimen170
\pgf@yx=\dimen171
\pgf@yy=\dimen172
\pgf@zx=\dimen173
\pgf@zy=\dimen174
LaTeX Font Info:    Trying to load font information for T1+qpl on input line 92
6.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tex-gyre\t1qpl.fd
File: t1qpl.fd 2009/09/25 v1.2 font definition file for T1/qpl
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen175
\pgf@path@lasty=\dimen176
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen177
\pgf@shorten@start@additional=\dimen178
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box58
\pgf@hbox=\box59
\pgf@layerbox@main=\box60
\pgf@picture@serial@count=\count283
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen179
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen180
\pgf@pt@y=\dimen181
\pgf@pt@temp=\dimen182
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen183
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen184
\pgf@sys@shading@range@num=\count284
\pgf@shadingcount=\count285
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box61
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pg
fcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmo
duleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box62
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmo
duleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\p
gfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen185
\pgf@nodesepend=\dimen186
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\p
gfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffo
r.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfke
ys.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
keys.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.
code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgf
for.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen187
\pgffor@skip=\dimen188
\pgffor@stack=\toks33
\pgffor@toks=\toks34
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer
/tikz\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgf
libraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count286
\pgfplotmarksize=\dimen189
)
\tikz@lastx=\dimen190
\tikz@lasty=\dimen191
\tikz@lastxsaved=\dimen192
\tikz@lastysaved=\dimen193
\tikz@lastmovetox=\dimen194
\tikz@lastmovetoy=\dimen195
\tikzleveldistance=\dimen196
\tikzsiblingdistance=\dimen197
\tikz@figbox=\box63
\tikz@figbox@bg=\box64
\tikz@tempbox=\box65
\tikz@tempbox@bg=\box66
\tikztreelevel=\count287
\tikznumberofchildren=\count288
\tikznumberofcurrentchild=\count289
\tikz@fig@count=\count290

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmo
dulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count291
\pgfmatrixcurrentcolumn=\coun