This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  4 SEP 2025 15:28
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./<PERSON><PERSON>_Ayorinde_Professional_CV_Simple.tex
(Toyese_Ayorinde_Professional_CV_Simple.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks20

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.s
ty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box52
\beforetitleunit=\skip51
\aftertitleunit=\skip52
\ttl@plus=\dimen150
\ttl@minus=\dimen151
\ttl@toksa=\toks21
\titlewidth=\dimen152
\titlewidthlast=\dimen153
\titlewidthfirst=\dimen154
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.s
ty
Package: enumitem 2025/01/19 v3.10 Customized lists
\labelindent=\skip53
\enit@outerparindent=\dimen155
\enit@toks=\toks22
\enit@inbox=\box53
\enit@count@id=\count271
\enitdp@description=\count272
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count273
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count274
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen156
\Hy@linkcounter=\count275
\Hy@pagecounter=\count276

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count277

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count278
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen157

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count279
\Field@Width=\dimen158
\Fld@charsize=\dimen159
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count280
\c@Item=\count281
\c@Hfootnote=\count282
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count283
\c@bookmark@seq@number=\count284

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.s
ty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip55
\f@nch@offset@elh=\skip56
\f@nch@offset@erh=\skip57
\f@nch@offset@olh=\skip58
\f@nch@offset@orh=\skip59
\f@nch@offset@elf=\skip60
\f@nch@offset@erf=\skip61
\f@nch@offset@olf=\skip62
\f@nch@offset@orf=\skip63
\f@nch@height=\skip64
\f@nch@footalignment=\skip65
\f@nch@widthL=\skip66
\f@nch@widthC=\skip67
\f@nch@widthR=\skip68
\@temptokenb=\toks23
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lastpage\lastpage.s
ty
Package: lastpage 2025/08/14 v2.1h lastpage: 2.09 or 2e? (HMM)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lastpage\lastpage2e
.sty
Package: lastpage2e 2025/08/14 v2.1h Decide which 2e lastpage version to use (H
MM)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lastpage\lastpagemo
dern.sty

LaTeX Warning: You have requested release `2024-11-01' of LaTeX,
               but only release `2024-06-01' is available.

Package: lastpagemodern 2025-08-14 v2.1h Refers to last page's name (HMM; JPG)
\c@lastpagecount=\count285
) 
))
Package hyperref Info: Option `colorlinks' set `true' on input line 27.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count286
\l__pdf_internal_box=\box54
)
No file Toyese_Ayorinde_Professional_CV_Simple.aux.
\openout1 = `Toyese_Ayorinde_Professional_CV_Simple.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 70.
LaTeX Font Info:    ... okay on input line 70.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(54.2025pt, 489.10287pt, 54.2025pt)
* v-part:(T,H,B)=(54.2025pt, 736.64185pt, 54.2025pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=489.10287pt
* \textheight=736.64185pt
* \oddsidemargin=-18.06749pt
* \evensidemargin=-18.06749pt
* \topmargin=-55.06749pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring ON on input line 70.
\@outlinefile=\write3
\openout3 = `Toyese_Ayorinde_Professional_CV_Simple.out'.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count287
\scratchdimen=\dimen160
\scratchbox=\box55
\nofMPsegments=\count288
\nofMParguments=\count289
\everyMPshowfont=\toks24
\MPscratchCnt=\count290
\MPscratchDim=\dimen161
\MPnumerator=\count291
\makeMPintoPDFobject=\count292
\everyMPtoPDFconversion=\toks25
)
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <10.95> on input line 78.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 78.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 78.

Underfull \hbox (badness 10000) in paragraph at lines 95--96

 []


Underfull \hbox (badness 10000) in paragraph at lines 97--98

 []


Underfull \hbox (badness 10000) in paragraph at lines 99--100

 []


Underfull \hbox (badness 10000) in paragraph at lines 101--102

 []


Underfull \hbox (badness 10000) in paragraph at lines 103--104

 []



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]


LaTeX Warning: Reference `LastPage' on page 2 undefined on input line 146.

[2]


LaTeX Warning: Reference `LastPage' on page 3 undefined on input line 177.

[3]
Underfull \hbox (badness 7468) in paragraph at lines 230--236
\T1/cmr/m/n/10.95 National Space Re-search and De-vel-op-ment
 []




LaTeX Warning: Reference `LastPage' on page 4 undefined on input line 238.

[4] 
enddocument/afterlastpage (AED): lastpage setting LastPage.

! Package lastpage Error: hyperref package version too old.

See the lastpage package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.238 \end{document}
                    
required version: 2024-10-30 or newer, found version:
2024-07-10 v7.01j Hypertext links for LaTeX
Update hyperref or use lastpageclassic.sty instead of
lastpagemodern.sty!

(Toyese_Ayorinde_Professional_CV_Simple.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.


Package rerunfilecheck Warning: File `Toyese_Ayorinde_Professional_CV_Simple.ou
t' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `Toyese_Ayorinde_Professional_CV_Sim
ple.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  75FFC59A87003D82B5AF16A50933A6C2;1627.

Package lastpage Warning: Rerun to get the 